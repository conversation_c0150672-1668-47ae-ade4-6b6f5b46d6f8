package biz

import (
	"context"
	"errors"
	"testing"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockAIRepo is a mock implementation of AIRepo
type MockAIRepo struct {
	mock.Mock
}

func (m *MockAIRepo) Chat(ctx context.Context, req *ChatRequest) (*ChatResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*ChatResponse), args.Error(1)
}

func (m *MockAIRepo) Analyze(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*AnalyzeResponse), args.Error(1)
}

func (m *MockAIRepo) ListModels(ctx context.Context) ([]*AIModel, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*AIModel), args.Error(1)
}

func (m *MockAIRepo) IsModelAvailable(ctx context.Context, modelName string) (bool, error) {
	args := m.Called(ctx, modelName)
	return args.Bool(0), args.Error(1)
}

func TestNewAIUsecase(t *testing.T) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	assert.NotNil(t, uc)
	assert.Equal(t, mockRepo, uc.repo)
}

func TestAIChat(t *testing.T) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Test case 1: Successful chat with default model
	req1 := &ChatRequest{Message: "Hello"}
	expectedResp1 := &ChatResponse{Response: "Hi there!", ModelUsed: "gemma-3-4b-it", TokensUsed: 10}
	mockRepo.On("IsModelAvailable", ctx, "gemma-3-4b-it").Return(true, nil).Once()
	mockRepo.On("Chat", ctx, req1).Return(expectedResp1, nil).Once()

	resp1, err1 := uc.Chat(ctx, req1)
	assert.NoError(t, err1)
	assert.Equal(t, expectedResp1, resp1)
	mockRepo.AssertExpectations(t)

	// Test case 2: Successful chat with specified model
	req2 := &ChatRequest{Message: "Hello", Model: "my-custom-model"}
	expectedResp2 := &ChatResponse{Response: "Hi from custom!", ModelUsed: "my-custom-model", TokensUsed: 12}
	mockRepo.On("IsModelAvailable", ctx, "my-custom-model").Return(true, nil).Once()
	mockRepo.On("Chat", ctx, req2).Return(expectedResp2, nil).Once()

	resp2, err2 := uc.Chat(ctx, req2)
	assert.NoError(t, err2)
	assert.Equal(t, expectedResp2, resp2)
	mockRepo.AssertExpectations(t)

	// Test case 3: Empty message
	req3 := &ChatRequest{Message: ""}
	resp3, err3 := uc.Chat(ctx, req3)
	assert.ErrorIs(t, err3, ErrInvalidAIRequest)
	assert.Nil(t, resp3)
	mockRepo.AssertNotCalled(t, "IsModelAvailable")
	mockRepo.AssertNotCalled(t, "Chat")

	// Test case 4: IsModelAvailable returns an error
	req4 := &ChatRequest{Message: "Test", Model: "error-model"}
	mockRepo.On("IsModelAvailable", ctx, "error-model").Return(false, errors.New("repo error")).Once()
	resp4, err4 := uc.Chat(ctx, req4)
	assert.Error(t, err4)
	assert.Contains(t, err4.Error(), "repo error")
	assert.Nil(t, resp4)
	mockRepo.AssertNotCalled(t, "Chat")

	// Test case 5: Model unavailable
	req5 := &ChatRequest{Message: "Test", Model: "unavailable-model"}
	mockRepo.On("IsModelAvailable", ctx, "unavailable-model").Return(false, nil).Once()
	resp5, err5 := uc.Chat(ctx, req5)
	assert.ErrorIs(t, err5, ErrAIModelUnavailable)
	assert.Nil(t, resp5)
	mockRepo.AssertNotCalled(t, "Chat")

	// Test case 6: Repo Chat returns an error
	req6 := &ChatRequest{Message: "Test", Model: "chat-error-model"}
	mockRepo.On("IsModelAvailable", ctx, "chat-error-model").Return(true, nil).Once()
	mockRepo.On("Chat", ctx, req6).Return(nil, errors.New("chat repo error")).Once()
	resp6, err6 := uc.Chat(ctx, req6)
	assert.Error(t, err6)
	assert.Contains(t, err6.Error(), "chat repo error")
	assert.Nil(t, resp6)
}

func TestAIAnalyze(t *testing.T) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Test case 1: Successful analysis with default type and model
	req1 := &AnalyzeRequest{Content: "Some text"}
	expectedResp1 := &AnalyzeResponse{Analysis: "Good", Confidence: 0.9, Metadata: map[string]string{"key": "value"}}
	mockRepo.On("Analyze", ctx, req1).Return(expectedResp1, nil).Once()

	resp1, err1 := uc.Analyze(ctx, req1)
	assert.NoError(t, err1)
	assert.Equal(t, expectedResp1, resp1)
	mockRepo.AssertExpectations(t)

	// Test case 2: Successful analysis with specified type and model
	req2 := &AnalyzeRequest{Content: "Another text", AnalysisType: "sentiment", Model: "my-analysis-model"}
	expectedResp2 := &AnalyzeResponse{Analysis: "Neutral", Confidence: 0.7}
	mockRepo.On("Analyze", ctx, req2).Return(expectedResp2, nil).Once()

	resp2, err2 := uc.Analyze(ctx, req2)
	assert.NoError(t, err2)
	assert.Equal(t, expectedResp2, resp2)
	mockRepo.AssertExpectations(t)

	// Test case 3: Empty content
	req3 := &AnalyzeRequest{Content: ""}
	resp3, err3 := uc.Analyze(ctx, req3)
	assert.ErrorIs(t, err3, ErrInvalidAIRequest)
	assert.Nil(t, resp3)
	mockRepo.AssertNotCalled(t, "Analyze")

	// Test case 4: Repo Analyze returns an error
	req4 := &AnalyzeRequest{Content: "Error text"}
	mockRepo.On("Analyze", ctx, req4).Return(nil, errors.New("analyze repo error")).Once()
	resp4, err4 := uc.Analyze(ctx, req4)
	assert.Error(t, err4)
	assert.Contains(t, err4.Error(), "analyze repo error")
	assert.Nil(t, resp4)
}

func TestAIListModels(t *testing.T) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	// Test case 1: Successful listing of models
	expectedModels := []*AIModel{
		{Name: "model1", Type: "text", Available: true},
		{Name: "model2", Type: "image", Available: false},
	}
	mockRepo.On("ListModels", ctx).Return(expectedModels, nil).Once()

	models, err := uc.ListModels(ctx)
	assert.NoError(t, err)
	assert.Equal(t, expectedModels, models)
	mockRepo.AssertExpectations(t)

	// Test case 2: Repo ListModels returns an error
	mockRepo.On("ListModels", ctx).Return(nil, errors.New("list models repo error")).Once()
	models, err = uc.ListModels(ctx)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "list models repo error")
	assert.Nil(t, models)
}

// Benchmarks
func BenchmarkAIChat(b *testing.B) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	req := &ChatRequest{Message: "Benchmark test message", Model: "gemma-3-4b-it"}
	expectedResp := &ChatResponse{Response: "Benchmark response", ModelUsed: "gemma-3-4b-it", TokensUsed: 20}

	mockRepo.On("IsModelAvailable", ctx, "gemma-3-4b-it").Return(true, nil)
	mockRepo.On("Chat", ctx, req).Return(expectedResp, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.Chat(ctx, req)
	}
}

func BenchmarkAIAnalyze(b *testing.B) {
	mockRepo := new(MockAIRepo)
	uc := NewAIUsecase(mockRepo, log.DefaultLogger)
	ctx := context.Background()

	req := &AnalyzeRequest{Content: "Benchmark analysis content", AnalysisType: "general", Model: "bielik-v3"}
	expectedResp := &AnalyzeResponse{Analysis: "Benchmark analysis", Confidence: 0.8}

	mockRepo.On("Analyze", ctx, req).Return(expectedResp, nil)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = uc.Analyze(ctx, req)
	}
}
